import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import _ from 'lodash';


const ComponentStatus = (props) => {
  const {
    allComponents,
    aggregatedReevaluationResult,
    componentListGroupMode,
  } = props;

  const { t } = useTranslation();

  const [passedComponentCount, setPassedComponentCount] = useState(0);
  const [failedComponentCount, setFailedComponentCount] = useState(0);
  const [undefinedComponentCount, setUndefinedComponentCount] = useState(0); // unhealthy component
  const [totalCount, setTotalCount] = useState(0);

  useEffect(() => {
    if (!allComponents || !_.isUndefined(aggregatedReevaluationResult)) return;

    if (_.isEmpty(allComponents)) {
      setPassedComponentCount(0);
      setFailedComponentCount(0);
      setUndefinedComponentCount(0);
      setTotalCount(0);
      return;
    }

    const mode = componentListGroupMode || 'component';

    const aggregatedMap = {}; // mode value to {num_passing, num_failing, contains_white}
    for (const r of aggregatedReevaluationResult || []) {
      if (mode === 'component' && _.isInteger(r.component_id)) {
        aggregatedMap[`${r.component_id}`] = r;
      } else if (mode === 'part' && !_.isEmpty(r.part_no)) {
        aggregatedMap[`${r.part_no}`] = r;
      } else if (mode === 'package' && !_.isEmpty(r.package_no)) {
        aggregatedMap[`${r.package_no}`] = r;
      }
    }

    let passedCount = 0;
    let failedCount = 0;
    let undefinedCount = 0;
    let total = 0;

    if (mode === 'component') {
      total = allComponents.length;
      for (const c of allComponents) {
        const numPass = _.get(aggregatedMap, [`${c.region_group_id}`, 'num_passing'], 0);
        const numFail = _.get(aggregatedMap, [`${c.region_group_id}`, 'num_failing'], 0);
        const containsWhite = _.get(aggregatedMap, [`${c.region_group_id}`, 'has_no_eval'], false);

        if (!c.healthy) {
          undefinedCount++;
        } else if (numFail > 0) {
          failedCount++;
        } else if (!containsWhite && numPass > 0) {
          passedCount++;
        }
      }
    } else if (mode === 'part') {
      for (const partNo of aggregatedMap) {
        const numPass = _.get(partNo, 'num_passing', 0);
        const numFail = _.get(partNo, 'num_failing', 0);
        const containsWhite = _.get(partNo, 'has_no_eval', false);

        total++;
        if (numFail > 0) {
          failedCount++;
        } else if (!containsWhite && numPass > 0) {
          passedCount++;
        }
      }
    } else if (mode === 'package') {
      for (const packageNo of aggregatedMap) {
        const numPass = _.get(packageNo, 'num_passing', 0);
        const numFail = _.get(packageNo, 'num_failing', 0);
        const containsWhite = _.get(packageNo, 'has_no_eval', false);

        total++;
        if (numFail > 0) {
          failedCount++;
        } else if (!containsWhite && numPass > 0) {
          passedCount++;
        }
      }
    }

    setPassedComponentCount(passedCount);
    setFailedComponentCount(failedCount);
    setUndefinedComponentCount(undefinedCount);
    setTotalCount(total);
  }, [allComponents, aggregatedReevaluationResult, componentListGroupMode]);

  return (
    <div style={{ width: '100%' }}>
      <div className='flex h-[30px] items-center self-stretch '>
        <div className='flex h-[30px] justify-center items-center gap-0.5 self-stretch px-3 py-1 rounded-[4px_4px_0px_0px]'>
          <span className='font-source text-xs font-normal leading-[normal] uppercase'>
            {t('common.information')}
          </span>
        </div>
      </div>
      <div className='flex flex-col justify-center items-start gap-1 self-stretch [background:#1E1E1E] p-2'>
        <div className='flex items-start gap-2 self-stretch'>
          <div className='flex items-center gap-2 flex-[1_0_0]'>
            <div className='flex w-[92px] items-center'>
              <div className='flex w-6 h-6 flex-col justify-center items-center gap-2.5 shrink-0 px-[3px] py-1.5'>
                <img
                  src='/icn/checkFilledCircle_green.svg'
                  className='w-[12px] h-[12px]'
                  alt='checkCircled'
                />
              </div>
              <span className='font-source text-xs font-normal leading-[150%] pt-0.5'>
                {t('common.passed')}
              </span>
            </div>
            <span className='text-gray-4 font-source text-xs font-normal leading-[150%]'>
              {passedComponentCount}/{totalCount}
            </span>
          </div>
          <div className='flex items-center gap-2 flex-[1_0_0]'>
            <div className='flex w-[92px] items-center'>
              <div className='flex w-6 h-6 flex-col justify-center items-center gap-2.5 shrink-0 px-[3px] py-1.5'>
                <img
                  src='/icn/failedCircled_red.svg'
                  className='w-[12px] h-[12px]'
                  alt='failedCircled'
                />
              </div>
              <span className='font-source text-xs font-normal leading-[150%] pt-0.5'>
                {t('common.failed')}
              </span>
            </div>
            <span className='text-gray-4 font-source text-xs font-normal leading-[150%]'>
              {failedComponentCount}
            </span>
          </div>
        </div>
        <div className='flex items-start gap-2 self-stretch'>
          {/* <div className='flex items-center gap-2 flex-[1_0_0]'>
            <div className='flex w-[92px] items-center'>
              <div className='flex w-6 h-6 flex-col justify-center items-center gap-2.5 shrink-0 px-[3px] py-1.5'>
                <img
                  src='/icn/undefined_purple.svg'
                  className='w-[12px] h-[12px]'
                  alt='undefined'
                />
              </div>
              <span className='font-source text-xs font-normal leading-[150%] pt-0.5'>
                {t('common.undefined')}
              </span>
            </div>
            <span className='text-gray-4 font-source text-xs font-normal leading-[150%]'>
              {}
            </span>
          </div> */}
          {componentListGroupMode === 'component' &&
            <div className='flex items-center gap-2 flex-[1_0_0]'>
              <div className='flex w-[92px] items-center'>
                <div className='flex w-6 h-6 flex-col justify-center items-center gap-2.5 shrink-0 px-[3px] py-1.5'>
                  <img
                    src='/icn/unknown_gray.svg'
                    className='w-[12px] h-[12px]'
                    alt='unknown'
                  />
                </div>
                <span className='font-source text-xs font-normal leading-[150%] pt-0.5'>
                  {t('common.unknown')}
                </span>
              </div>
              <span className='text-gray-4 font-source text-xs font-normal leading-[150%]'>
                {undefinedComponentCount}
              </span>
            </div>
          }
        </div>
      </div>
    </div>
  );
};

export default ComponentStatus;