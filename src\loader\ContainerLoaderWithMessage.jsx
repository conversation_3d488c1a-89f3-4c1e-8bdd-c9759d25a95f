import { Alert, Spin } from 'antd';
import _ from 'lodash';
import React from 'react';
import { useSelector } from 'react-redux';
import styled from 'styled-components';


const SelectedDisabledContainer = styled.div`
  user-select: none;
`;

const ContainerLoadingWithMessage = () => {
  const isContainerLvlLoadingEnabled = useSelector((state) => state.setting.isContainerLvlLoadingEnabled);
  const containerLvlLoadingMsg = useSelector((state) => state.setting.containerLvlLoadingMsg);

  return (
    <SelectedDisabledContainer
      className={`absolute top-0 left-0 w-full h-full bg-[#03030380] ${isContainerLvlLoadingEnabled ? 'z-[1001]' : 'hidden'}`}
    >
      <div className='flex flex-col items-center justify-center w-full h-full'>
        <Alert
          type='info'
          icon={<Spin size='large' />}
          message={
            <span className='font-source text-[14px] font-semibold'>
              {!_.isEmpty(containerLvlLoadingMsg) ? containerLvlLoadingMsg : 'Loading...'}
            </span>
          }
        />
      </div>
    </SelectedDisabledContainer>
  );
};

export default ContainerLoadingWithMessage;